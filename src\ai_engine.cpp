#include "../include/ai_engine.h"
#include "../include/board.h"
#include "../include/arc_capture.h"

// 打印弧线吃子分析（调试用）- 临时禁用
void printArcCaptureAnalysis() {
    cout << "=== 弧线吃子分析（已禁用）===" << endl;
    cout << "弧线吃子功能暂时禁用以测试基本功能" << endl;
    cout << "===================" << endl;
}

// 改进的位置评估函数
int evaluatePosition() {
    int myPieces = countPieces(computerSide);
    int opponentPieces = countPieces(computerSide ^ 1);

    int score = 0;

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    // 位置评估：中心位置更有价值
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (Board[i][j] == (computerSide ^ 1)) {
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    return score;
}

// 基于实时棋盘状态的移动生成算法 - 加强版本
Step generateBestMove() {
    // 调试模式（发布时设为false）
    bool debugMode = false;

    if (debugMode) {
        cout << "\n=== 开始生成己方移动 ===" << endl;
        cout << "当前棋盘状态 (computerSide=" << computerSide << "):" << endl;
        printBoard();

        // 详细分析棋盘状态
        cout << "棋盘状态分析:" << endl;
        int myCount = 0, opponentCount = 0, emptyCount = 0;
        for (int i = 0; i < BOARD_SIZE; i++) {
            for (int j = 0; j < BOARD_SIZE; j++) {
                if (Board[i][j] == computerSide) {
                    myCount++;
                    cout << "  己方棋子在: (" << i << "," << j << ")" << endl;
                } else if (Board[i][j] == (computerSide ^ 1)) {
                    opponentCount++;
                    cout << "  对方棋子在: (" << i << "," << j << ")" << endl;
                } else if (Board[i][j] == EMPTY) {
                    emptyCount++;
                }
            }
        }
        cout << "统计: 己方=" << myCount << ", 对方=" << opponentCount << ", 空位=" << emptyCount << endl;
    }

    Step bestMove(Point(-1, -1), Point(-1, -1), -10000);
    vector<Step> validMoves;

    // 第一步：扫描棋盘，找到所有己方棋子的实际位置
    vector<Point> myPieces;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                myPieces.push_back(Point(i, j));
                if (debugMode) {
                    cout << "找到己方棋子在: (" << i << "," << j << ")" << endl;
                }
            }
        }
    }

    if (debugMode) {
        cout << "总共找到 " << myPieces.size() << " 个己方棋子" << endl;
    }

    // 如果没有找到己方棋子，返回无效移动
    if (myPieces.empty()) {
        if (debugMode) {
            cout << "错误：没有找到任何己方棋子！" << endl;
        }
        return bestMove;
    }

    // 第二步：对每个己方棋子，检查所有可能的合法移动
    for (const Point& start : myPieces) {
        if (debugMode) {
            cout << "检查棋子 (" << start.x << "," << start.y << ") 的可能移动..." << endl;
        }

        // 再次确认这个位置确实有己方棋子
        if (Board[start.x][start.y] != computerSide) {
            if (debugMode) {
                cout << "警告：位置 (" << start.x << "," << start.y << ") 不再有己方棋子！" << endl;
                cout << "  实际棋子值: " << Board[start.x][start.y] << ", 期望: " << computerSide << endl;
            }
            continue;
        }

        if (debugMode) {
            cout << "验证通过：位置 (" << start.x << "," << start.y << ") 确实有己方棋子" << endl;
        }

        // 检查8方向的普通移动
        for (int di = -1; di <= 1; di++) {
            for (int dj = -1; dj <= 1; dj++) {
                if (di == 0 && dj == 0) continue;

                int newX = start.x + di;
                int newY = start.y + dj;

                // 检查边界
                if (newX < 0 || newX >= BOARD_SIZE || newY < 0 || newY >= BOARD_SIZE) {
                    continue;
                }

                Point end(newX, newY);

                // 检查目标位置是否为空
                if (Board[newX][newY] == EMPTY) {
                    // 这是一个合法的普通移动
                    int value = evaluateMove(start, end);

                    // 双重验证移动的合法性
                    if (isValidMove(start, end)) {
                        validMoves.push_back(Step(start, end, value));
                        if (debugMode) {
                            cout << "  找到普通移动: (" << start.x << "," << start.y << ") -> ("
                                 << newX << "," << newY << ") 评分:" << value << endl;
                        }
                    } else {
                        if (debugMode) {
                            cout << "  移动验证失败: (" << start.x << "," << start.y << ") -> ("
                                 << newX << "," << newY << ")" << endl;
                        }
                    }
                } else {
                    if (debugMode) {
                        cout << "  目标位置(" << newX << "," << newY << ")不为空，棋子值: "
                             << Board[newX][newY] << endl;
                    }
                }
            }
        }

        // 检查弧线吃子移动（使用简化实现）
        for (int ti = 0; ti < BOARD_SIZE; ti++) {
            for (int tj = 0; tj < BOARD_SIZE; tj++) {
                // 检查目标位置是否有对方棋子
                if (Board[ti][tj] == (computerSide ^ 1)) {
                    Point end(ti, tj);

                    if (debugMode) {
                        cout << "  检查吃子可能: (" << start.x << "," << start.y << ") -> ("
                             << ti << "," << tj << ")" << endl;
                    }

                    // 检查是否可以通过弧线吃掉这个棋子
                    if (canCapture(start, end)) {
                        int value = evaluateMove(start, end) + 300; // 吃子奖励
                        validMoves.push_back(Step(start, end, value));
                        if (debugMode) {
                            cout << "  ✓ 找到吃子移动: (" << start.x << "," << start.y << ") -> ("
                                 << ti << "," << tj << ") 评分:" << value << endl;
                        }
                    } else {
                        if (debugMode) {
                            cout << "  ✗ 吃子验证失败: (" << start.x << "," << start.y << ") -> ("
                                 << ti << "," << tj << ")" << endl;
                        }
                    }
                }
            }
        }
    }

    // 第三步：从所有合法移动中选择最佳的
    if (debugMode) {
        cout << "总共找到 " << validMoves.size() << " 个合法移动" << endl;
    }

    // 改进的移动选择策略：不只选择最高分，而是从高分移动中随机选择
    if (!validMoves.empty()) {
        // 按评分排序
        sort(validMoves.begin(), validMoves.end(), [](const Step& a, const Step& b) {
            return a.value > b.value;
        });

        // 选择策略：70%概率选择前25%的移动，30%概率选择其他移动
        int topCount = max(1, (int)(validMoves.size() * 0.25));
        int randomChoice = rand() % 100;

        if (randomChoice < 70 && topCount > 0) {
            // 从前25%的高分移动中随机选择
            int randomIndex = rand() % topCount;
            bestMove = validMoves[randomIndex];
        } else {
            // 从所有移动中随机选择（增加多样性）
            int randomIndex = rand() % validMoves.size();
            bestMove = validMoves[randomIndex];
        }

        if (debugMode) {
            cout << "选择策略：从" << validMoves.size() << "个移动中选择，"
                 << "前" << topCount << "个高分移动，随机选择概率："
                 << (randomChoice < 70 ? "高分" : "随机") << endl;
        }
    }

    // 第四步：最终验证选中的移动
    if (bestMove.start.x != -1) {
        if (debugMode) {
            cout << "选择最佳移动: (" << bestMove.start.x << "," << bestMove.start.y
                 << ") -> (" << bestMove.end.x << "," << bestMove.end.y << ") 评分:" << bestMove.value << endl;
        }

        // 最终验证：确保起始位置仍然有己方棋子
        if (Board[bestMove.start.x][bestMove.start.y] != computerSide) {
            if (debugMode) {
                cout << "✗ 错误：最佳移动的起始位置没有己方棋子！" << endl;
                cout << "  起始位置(" << bestMove.start.x << "," << bestMove.start.y
                     << ")实际棋子: " << Board[bestMove.start.x][bestMove.start.y]
                     << ", 期望: " << computerSide << endl;
                cout << "重新寻找安全移动..." << endl;
            }
            bestMove = findAnyValidMove();
        } else {
            if (debugMode) {
                cout << "✓ 最终验证通过：起始位置有己方棋子" << endl;
                cout << "  目标位置(" << bestMove.end.x << "," << bestMove.end.y
                     << ")棋子: " << Board[bestMove.end.x][bestMove.end.y] << endl;
            }
        }
    } else {
        if (debugMode) {
            cout << "没有找到合法移动，使用备选方案..." << endl;
        }
        bestMove = findAnyValidMove();
    }

    if (debugMode) {
        cout << "=== 移动生成完成 ===" << endl;
    }

    return bestMove;
}

// 改进的移动评估函数，增加更多随机性和策略多样性
int evaluateMove(Point start, Point end) {
    int score = 0;

    // 基础分数（增加随机性）
    score += 10 + (rand() % 20);

    // 中心位置奖励（随机化）
    if (end.x >= 2 && end.x <= 3 && end.y >= 2 && end.y <= 3) {
        score += 15 + (rand() % 15);
    }

    // 边缘位置策略（有时边缘也有价值）
    if (end.x == 0 || end.x == BOARD_SIZE-1 || end.y == 0 || end.y == BOARD_SIZE-1) {
        // 有时边缘位置也有战略价值
        score += (rand() % 20) - 10; // -10到+10的随机值
    }

    // 角落位置特殊考虑（弧线吃子的起点）
    if ((end.x <= 1 && end.y <= 1) || (end.x <= 1 && end.y >= 4) ||
        (end.x >= 4 && end.y <= 1) || (end.x >= 4 && end.y >= 4)) {
        score += 5 + (rand() % 10); // 角落位置有弧线优势
    }

    // 距离起始位置的考虑（鼓励多样化移动）
    int distance = abs(end.x - start.x) + abs(end.y - start.y);
    score += distance * (1 + rand() % 3); // 距离越远，有时评分越高

    // 添加大的随机变化以增加移动多样性
    score += (rand() % 50) - 25; // -25到+25的随机值

    return score;
}

// 在指定棋盘上进行位置评估（不修改全局棋盘）
int evaluatePositionOnBoard(int board[BOARD_SIZE][BOARD_SIZE]) {
    int myPieces = 0;
    int opponentPieces = 0;
    int score = 0;

    // 计算棋子数量和位置评估
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == computerSide) {
                myPieces++;
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (board[i][j] == (computerSide ^ 1)) {
                opponentPieces++;
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    return score;
}

// 基于实时棋盘状态的安全移动查找 - 加强版本
Step findAnyValidMove() {
    bool debugMode = false; // 调试模式（发布时设为false）

    if (debugMode) {
        cout << "执行安全移动查找..." << endl;
        cout << "当前棋盘状态:" << endl;
        printBoard();
    }

    // 重新扫描棋盘，找到所有己方棋子
    vector<Point> myPieces;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                myPieces.push_back(Point(i, j));
                if (debugMode) {
                    cout << "安全查找：找到己方棋子在 (" << i << "," << j << ")" << endl;
                }
            }
        }
    }

    if (myPieces.empty()) {
        if (debugMode) {
            cout << "安全查找：没有找到任何己方棋子！" << endl;
        }
        return Step(Point(-1, -1), Point(-1, -1), -1);
    }

    if (debugMode) {
        cout << "找到 " << myPieces.size() << " 个己方棋子，开始寻找合法移动..." << endl;
    }

    // 第一优先级：寻找普通移动（收集所有可能的移动）
    vector<Step> possibleMoves;

    for (const Point& start : myPieces) {
        // 再次确认这个位置有己方棋子
        if (Board[start.x][start.y] != computerSide) {
            if (debugMode) {
                cout << "警告：位置 (" << start.x << "," << start.y << ") 棋子状态改变！" << endl;
            }
            continue;
        }

        // 尝试8个方向的移动
        for (int di = -1; di <= 1; di++) {
            for (int dj = -1; dj <= 1; dj++) {
                if (di == 0 && dj == 0) continue;

                int newX = start.x + di;
                int newY = start.y + dj;

                // 检查边界
                if (newX >= 0 && newX < BOARD_SIZE &&
                    newY >= 0 && newY < BOARD_SIZE) {

                    // 检查目标位置是否为空
                    if (Board[newX][newY] == EMPTY) {
                        Point end(newX, newY);
                        possibleMoves.push_back(Step(start, end, 0));
                        if (debugMode) {
                            cout << "安全查找：找到普通移动 (" << start.x << "," << start.y
                                 << ") -> (" << newX << "," << newY << ")" << endl;
                        }
                    }
                }
            }
        }
    }

    // 如果找到普通移动，随机选择一个
    if (!possibleMoves.empty()) {
        int randomIndex = rand() % possibleMoves.size();
        return possibleMoves[randomIndex];
    }

    // 第二优先级：寻找吃子移动（收集所有可能的吃子移动）
    vector<Step> captureMoves;

    for (const Point& start : myPieces) {
        // 再次确认这个位置有己方棋子
        if (Board[start.x][start.y] != computerSide) {
            continue;
        }

        // 寻找可以吃掉的对方棋子
        for (int ti = 0; ti < BOARD_SIZE; ti++) {
            for (int tj = 0; tj < BOARD_SIZE; tj++) {
                if (Board[ti][tj] == (computerSide ^ 1)) {
                    Point end(ti, tj);
                    if (canCapture(start, end)) {
                        captureMoves.push_back(Step(start, end, 100));
                        if (debugMode) {
                            cout << "安全查找：找到吃子移动 (" << start.x << "," << start.y
                                 << ") -> (" << ti << "," << tj << ")" << endl;
                        }
                    }
                }
            }
        }
    }

    // 如果找到吃子移动，随机选择一个
    if (!captureMoves.empty()) {
        int randomIndex = rand() % captureMoves.size();
        return captureMoves[randomIndex];
    }

    // 第三优先级：最后的尝试 - 寻找任何可能的移动，即使不是最优的
    if (debugMode) {
        cout << "尝试最后的备选方案..." << endl;
    }

    for (const Point& start : myPieces) {
        // 再次确认这个位置有己方棋子
        if (Board[start.x][start.y] != computerSide) {
            continue;
        }

        // 尝试所有可能的目标位置（更宽松的检查）
        for (int ti = 0; ti < BOARD_SIZE; ti++) {
            for (int tj = 0; tj < BOARD_SIZE; tj++) {
                if (ti == start.x && tj == start.y) continue; // 不能移动到自己位置

                Point end(ti, tj);

                // 检查是否是8方向一格的移动到空位置
                int dx = abs(ti - start.x);
                int dy = abs(tj - start.y);

                if (((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1))
                    && Board[ti][tj] == EMPTY) {
                    if (debugMode) {
                        cout << "最后备选：找到基础移动 (" << start.x << "," << start.y
                             << ") -> (" << ti << "," << tj << ")" << endl;
                    }
                    return Step(start, end, 0);
                }
            }
        }
    }

    // 最后的最后：如果真的没有找到任何移动，尝试最宽松的策略
    for (const Point& start : myPieces) {
        if (Board[start.x][start.y] != computerSide) {
            continue;
        }

        // 尝试移动到任何空位置，不管距离
        for (int ti = 0; ti < BOARD_SIZE; ti++) {
            for (int tj = 0; tj < BOARD_SIZE; tj++) {
                if (Board[ti][tj] == EMPTY) {
                    Point end(ti, tj);
                    if (debugMode) {
                        cout << "最终尝试：任意移动 (" << start.x << "," << start.y
                             << ") -> (" << ti << "," << tj << ")" << endl;
                    }
                    return Step(start, end, 0);
                }
            }
        }
    }

    if (debugMode) {
        cout << "安全查找：真的没有找到任何合法移动！" << endl;
    }
    return Step(Point(-1, -1), Point(-1, -1), -1);
}
