# 苏拉卡尔塔棋引擎 (Surakarta Chess Engine)

## 项目简介

这是一个基于SAU通用计算机博弈对战平台的苏拉卡尔塔棋引擎实现。该引擎采用模块化设计，严格遵循比赛规则，支持标准的通信协议，可以与SAU Game Platform进行对战。

## 比赛规则

1. 参赛者掷硬币决定由谁先开始，每次只能移动一个棋子，两人轮流走棋
2. 每个棋子可以向8个方向（上、下、左、右、左上、左下、右上、右下）移动一格（当所去的方向无棋子时）
3. 若要吃掉对方棋子，必须经过至少一个完整的弧线，并且移动路径中不可以有本方棋子阻挡
4. 黑子可以吃掉白子，同样白子沿同一路径的相反方向也可以吃掉黑子
5. 当一方棋子全部被吃掉时棋局结束，有剩余棋子方获胜
6. 当双方都不能再吃掉对方棋子时，剩余棋子多的一方获胜

## 棋盘坐标

- 棋盘从左到右共6列（A-F），从上到下共6行（1-6）
- 第1列第2行的棋子坐标表示为AB
- 初始状态：黑子占据前两行，白子占据后两行

## 项目结构

```plaintext
SurakartaChess-Platform/
├── src/                    # 源代码目录
│   ├── main.cpp           # 主程序入口
│   ├── board.cpp          # 棋盘管理模块
│   ├── ai_engine.cpp      # AI算法模块（核心决策）
│   ├── arc_capture.cpp    # 弧线吃子逻辑
│   └── protocol.cpp       # SAU平台通信协议
├── include/               # 头文件目录
│   ├── common.h          # 公共定义和数据结构
│   ├── board.h           # 棋盘管理接口
│   ├── ai_engine.h       # AI算法接口
│   ├── arc_capture.h     # 弧线吃子接口
│   └── protocol.h        # 通信协议接口
├── docs/                 # 文档目录
│   └── 通信协议说明与引擎编写规范.txt
├── build.bat            # 编译脚本
└── README.md            # 项目说明
```

## 模块化设计

### 核心模块

1. **AI算法模块 (`src/ai_engine.cpp`)**
   - 这是引擎的核心决策模块
   - 包含位置评估函数和最佳移动生成算法
   - **后期可以通过仅在该文件中添加完善博弈算法以提升引擎的性能**
   - 支持Alpha-Beta剪枝、深度搜索等高级算法扩展

2. **棋盘管理模块 (`src/board.cpp`)**
   - 负责棋盘状态维护和移动验证
   - 实现8方向基本移动规则
   - 提供棋盘复制、棋子计数等工具函数

3. **弧线吃子模块 (`src/arc_capture.cpp`)**
   - 实现苏拉卡尔塔棋特有的弧线吃子逻辑
   - 支持四个角落弧线的路径追踪和阻挡检查

4. **通信协议模块 (`src/protocol.cpp`)**
   - 处理与SAU平台的通信协议
   - 解析命令并调用相应的处理函数

## 编译和运行

### 前置要求

- Windows操作系统
- G++编译器（推荐MinGW或TDM-GCC）
- 支持C++11标准

### 编译步骤

1. 确保已安装G++编译器
2. 双击运行 `build.bat` 脚本
3. 编译成功后会生成 `surakarta_engine.exe` 文件

### 使用方法

1. 将生成的 `surakarta_engine.exe` 文件加载到SAU Game Platform中
2. 引擎会自动响应平台的通信协议命令
3. 支持的命令包括：
   - `name?` - 返回引擎名称
   - `new black/white` - 开始新游戏并分配颜色
   - `move XXYY` - 接收对手移动并返回己方移动
   - `error` - 处理错误着法
   - `end` - 游戏结束
   - `quit` - 退出引擎

## AI算法优化指南

要提升引擎性能，主要关注 `src/ai_engine.cpp` 文件中的以下函数：

1. **`evaluatePosition()`** - 位置评估函数
   - 可以添加更多评估因子（如棋子活动性、威胁评估等）
   - 调整各项评估权重

2. **`generateBestMove()`** - 最佳移动生成
   - 可以实现Alpha-Beta剪枝算法
   - 添加深度搜索和时间控制
   - 实现开局库和残局库

### 示例优化方向

```cpp
// 在ai_engine.cpp中可以添加：
// - Minimax算法与Alpha-Beta剪枝
// - 迭代加深搜索
// - 置换表优化
// - 移动排序优化
// - 静态搜索扩展
```

## 技术特性

- 模块化设计，易于维护和扩展
- 严格遵循SAU平台通信协议
- 实现了8方向基本移动规则
- 支持完整的弧线吃子逻辑
- 可扩展的AI决策框架
- 清晰的代码结构和接口设计

## 许可证

本项目仅用于学习和比赛目的。