#ifndef COMMON_H
#define COMMON_H

#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <vector>
#include <algorithm>

using namespace std;

// 棋子类型定义
#define BLACK 0
#define WHITE 1
#define EMPTY 2

// 棋盘大小
#define BOARD_SIZE 6

// 点结构
struct Point {
    int x, y;
    Point() : x(0), y(0) {}
    Point(int x, int y) : x(x), y(y) {}
};

// 步结构
struct Step {
    Point start, end;
    int value;
    Step() : value(0) {}
    Step(Point s, Point e, int v = 0) : start(s), end(e), value(v) {}
};

// 全局变量声明
extern int Board[BOARD_SIZE][BOARD_SIZE];  // 棋盘结构
extern int computerSide;                   // 己方执棋颜色
extern int gameStarted;                    // 对局开始标记

#endif // COMMON_H
