#ifndef PROTOCOL_H
#define PROTOCOL_H

#include "common.h"

// SAU平台通信协议相关函数声明
void handleMoveCommand();
void handleNewCommand();
void handleErrorCommand();
void handleNameQuery();
void handleEndCommand();
void handleQuitCommand();

// 安全移动执行函数
bool executeSafeMove(Step step);

// 处理对方移动的专用函数
bool processOpponentMove(Step opponentMove);

// 强制执行对方移动以保持棋盘同步
void forceOpponentMove(Step opponentMove);

// 主通信循环
int runProtocolLoop();

#endif // PROTOCOL_H
