#ifndef BOARD_H
#define BOARD_H

#include "common.h"

// 棋盘管理相关函数声明
void initializeBoard();
void printBoard();
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]);
int countPieces(int color);
int isGameOver();
int hasValidMoves(int side);

// 移动验证相关函数声明
int isValidMove(Point start, Point end);
int canCapture(Point start, Point end);
int makeMove(Point start, Point end);
bool validateMove(Point start, Point end);

// 简化的弧线吃子函数
int isSimpleArcCapture(int startX, int startY, int endX, int endY);
int isOnCornerArc(int startX, int startY, int endX, int endY, int cornerX, int cornerY);

#endif // BOARD_H
