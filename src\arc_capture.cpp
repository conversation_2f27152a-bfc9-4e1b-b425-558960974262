// 苏拉卡尔塔弧线吃子逻辑实现
//
// 苏拉卡尔塔棋的弧线定义：
// 棋盘四个角落有弧线环路，棋子可以通过弧线路径进行远距离移动和吃子
//
// 苏拉卡尔塔棋盘布局（6x6）：
//   A B C D E F
// A ●-●-○-○-●-●  (0行)
// B ●-○-○-○-○-●  (1行)
// C ○-○-○-○-○-○  (2行)
// D ○-○-○-○-○-○  (3行)
// E ●-○-○-○-○-●  (4行)
// F ●-●-○-○-●-●  (5行)
//
// 四个弧线环路：
// 1. 左上环路：连接(0,0)-(0,1)-(1,1)-(1,0)的弧形路径
// 2. 右上环路：连接(0,4)-(0,5)-(1,5)-(1,4)的弧形路径
// 3. 左下环路：连接(4,0)-(5,0)-(5,1)-(4,1)的弧形路径
// 4. 右下环路：连接(4,4)-(5,4)-(5,5)-(4,5)的弧形路径

#include "../include/arc_capture.h"

// 外部变量声明
extern int Board[BOARD_SIZE][BOARD_SIZE];
extern int computerSide;

// 苏拉卡尔塔棋的四个弧线环路定义
// 每个环路包含4个角落位置，按顺时针方向排列

// 左上角弧线环路（顺时针）
vector<ArcPoint> topLeftArc = {
    ArcPoint(0, 0), ArcPoint(0, 1), ArcPoint(1, 1), ArcPoint(1, 0)
};

// 右上角弧线环路（顺时针）
vector<ArcPoint> topRightArc = {
    ArcPoint(0, 4), ArcPoint(0, 5), ArcPoint(1, 5), ArcPoint(1, 4)
};

// 左下角弧线环路（顺时针）
vector<ArcPoint> bottomLeftArc = {
    ArcPoint(4, 0), ArcPoint(5, 0), ArcPoint(5, 1), ArcPoint(4, 1)
};

// 右下角弧线环路（顺时针）
vector<ArcPoint> bottomRightArc = {
    ArcPoint(4, 4), ArcPoint(5, 4), ArcPoint(5, 5), ArcPoint(4, 5)
};

// 所有弧线的集合
vector<vector<ArcPoint>*> allArcs = {&topLeftArc, &topRightArc, &bottomLeftArc, &bottomRightArc};

// 检查一个位置是否在弧线环路上
bool isOnArc(int x, int y, const vector<ArcPoint>& arc) {
    for (const auto& point : arc) {
        if (x == point.x && y == point.y) {
            return true;
        }
    }
    return false;
}

// 获取位置在弧线中的索引
int getArcIndex(int x, int y, const vector<ArcPoint>& arc) {
    for (size_t i = 0; i < arc.size(); i++) {
        if (x == arc[i].x && y == arc[i].y) {
            return i;
        }
    }
    return -1;
}

// 检查弧线路径上是否有阻挡（改进版本）
bool isArcPathClear(const vector<ArcPoint>& arc, int startIdx, int endIdx) {
    if (startIdx == -1 || endIdx == -1 || startIdx == endIdx) {
        return false;
    }

    int arcSize = arc.size();

    // 计算两个方向的路径长度
    int clockwiseSteps = (endIdx - startIdx + arcSize) % arcSize;
    int counterClockwiseSteps = (startIdx - endIdx + arcSize) % arcSize;

    // 选择较短的路径
    bool useClockwise = clockwiseSteps <= counterClockwiseSteps;
    int steps = useClockwise ? clockwiseSteps : counterClockwiseSteps;

    // 检查路径上的每个点（不包括起点和终点）
    for (int i = 1; i < steps; i++) {
        int checkIdx;
        if (useClockwise) {
            checkIdx = (startIdx + i) % arcSize;
        } else {
            checkIdx = (startIdx - i + arcSize) % arcSize;
        }

        int checkX = arc[checkIdx].x;
        int checkY = arc[checkIdx].y;

        // 如果路径上有任何棋子（己方或对方），则路径被阻挡
        if (Board[checkX][checkY] != EMPTY) {
            return false;
        }
    }

    return true;
}

// 检查是否可以通过弧线从起点到达终点
bool canReachThroughArc(int startX, int startY, int endX, int endY) {
    // 如果起点和终点相同，不能移动
    if (startX == endX && startY == endY) {
        return false;
    }

    // 检查每个弧线环路
    for (const auto& arc : allArcs) {
        // 检查起点和终点是否都在这个弧线上
        int startIdx = getArcIndex(startX, startY, *arc);
        int endIdx = getArcIndex(endX, endY, *arc);

        if (startIdx != -1 && endIdx != -1 && startIdx != endIdx) {
            // 检查弧线路径是否畅通
            if (isArcPathClear(*arc, startIdx, endIdx)) {
                return true;
            }
        }
    }

    return false;
}

// 使用BFS寻找通过弧线的复合路径（从任意位置到弧线，通过弧线，再到目标位置）
bool findComplexArcPath(int startX, int startY, int endX, int endY) {
    // 如果起点和终点相同，不能移动
    if (startX == endX && startY == endY) {
        return false;
    }

    // 首先检查直接的弧线路径
    if (canReachThroughArc(startX, startY, endX, endY)) {
        return true;
    }

    // 检查复合路径：起点 -> 弧线入口 -> 弧线出口 -> 终点
    for (const auto& arc : allArcs) {
        for (const auto& arcPoint : *arc) {
            // 检查是否可以从起点直线到达弧线上的某点
            if (canMoveDirectly(startX, startY, arcPoint.x, arcPoint.y)) {
                // 检查是否可以从弧线上的某点到达终点
                for (const auto& exitPoint : *arc) {
                    if (canReachThroughArc(arcPoint.x, arcPoint.y, exitPoint.x, exitPoint.y) &&
                        canMoveDirectly(exitPoint.x, exitPoint.y, endX, endY)) {
                        return true;
                    }
                }
            }
        }
    }

    return false;
}

// 检查是否可以直线移动（8个方向，任意距离，路径无阻挡）
bool canMoveDirectly(int startX, int startY, int endX, int endY) {
    if (startX == endX && startY == endY) {
        return false;
    }

    int dx = endX - startX;
    int dy = endY - startY;

    // 检查是否是直线移动（水平、垂直或对角线）
    if (dx != 0 && dy != 0 && abs(dx) != abs(dy)) {
        return false; // 不是直线移动
    }

    // 计算移动方向
    int stepX = (dx == 0) ? 0 : (dx > 0 ? 1 : -1);
    int stepY = (dy == 0) ? 0 : (dy > 0 ? 1 : -1);

    // 检查路径上是否有阻挡
    int currentX = startX + stepX;
    int currentY = startY + stepY;

    while (currentX != endX || currentY != endY) {
        if (currentX < 0 || currentX >= BOARD_SIZE ||
            currentY < 0 || currentY >= BOARD_SIZE) {
            return false; // 超出边界
        }

        if (Board[currentX][currentY] != EMPTY) {
            return false; // 路径被阻挡
        }

        currentX += stepX;
        currentY += stepY;
    }

    return true;
}


// 检查是否可以通过弧线进行吃子的主函数（改进版本）
bool isValidArcCapture(int startX, int startY, int endX, int endY) {
    // 检查边界
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有己方棋子
    if (Board[startX][startY] != computerSide) {
        return false;
    }

    // 检查目标位置是否有对方棋子
    if (Board[endX][endY] != (computerSide ^ 1)) {
        return false;
    }

    // 如果是8方向一格的移动，不是弧线吃子
    int dx = abs(endX - startX);
    int dy = abs(endY - startY);
    if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
        return false;
    }

    // 使用改进的弧线路径查找
    return findComplexArcPath(startX, startY, endX, endY);
}

// 检查是否可以通过弧线进行移动（不一定是吃子）
bool isValidArcMove(int startX, int startY, int endX, int endY) {
    // 检查边界
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有棋子
    if (Board[startX][startY] == EMPTY) {
        return false;
    }

    // 如果是8方向一格的移动，不是弧线移动
    int dx = abs(endX - startX);
    int dy = abs(endY - startY);
    if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
        return false;
    }

    // 使用改进的弧线路径查找
    return findComplexArcPath(startX, startY, endX, endY);
}

// 获取所有可能的弧线吃子移动
vector<pair<ArcPoint, ArcPoint>> getAllArcCaptureMoves(int side) {
    vector<pair<ArcPoint, ArcPoint>> moves;

    // 遍历棋盘上的所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == side) {
                ArcPoint start(i, j);

                // 检查所有可能的目标位置（对方棋子）
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (side ^ 1)) {
                            ArcPoint end(ti, tj);

                            // 临时设置computerSide以便检查
                            int originalSide = computerSide;
                            computerSide = side;

                            if (isValidArcCapture(i, j, ti, tj)) {
                                moves.push_back(make_pair(start, end));
                            }

                            // 恢复原始设置
                            computerSide = originalSide;
                        }
                    }
                }
            }
        }
    }

    return moves;
}

// 调试函数：打印弧线信息
void printArcInfo() {
    cout << "苏拉卡尔塔棋弧线信息:" << endl;
    cout << "左上角弧线: ";
    for (const auto& point : topLeftArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "右上角弧线: ";
    for (const auto& point : topRightArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "左下角弧线: ";
    for (const auto& point : bottomLeftArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "右下角弧线: ";
    for (const auto& point : bottomRightArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;
}