// 弧线吃子逻辑实现
// 苏拉卡尔塔棋的弧线定义：
// 棋盘四个角落有弧线，棋子必须经过至少一个完整的弧线才能吃子
//
// 苏拉卡尔塔棋盘布局：
//   A B C D E F
// A ○-○ ○ ○ ○-○
// B ○ ○ ○ ○ ○ ○
// C ○ ○ ○ ○ ○ ○
// D ○ ○ ○ ○ ○ ○
// E ○ ○ ○ ○ ○ ○
// F ○-○ ○ ○ ○-○
//
// 四个角落的弧线连接相邻的四个点

#include "../include/arc_capture.h"

// 外部变量声明
extern int Board[BOARD_SIZE][BOARD_SIZE];
extern int computerSide;

// 苏拉卡尔塔棋的四个角落弧线
// 每个弧线包含4个相邻的角落位置，形成一个小方形的弧线路径

// 左上角弧线路径（顺时针）
vector<ArcPoint> topLeftArc = {
    ArcPoint(0, 0), ArcPoint(0, 1), ArcPoint(1, 1), ArcPoint(1, 0)
};

// 右上角弧线路径（顺时针）
vector<ArcPoint> topRightArc = {
    ArcPoint(0, 5), ArcPoint(0, 4), ArcPoint(1, 4), ArcPoint(1, 5)
};

// 左下角弧线路径（顺时针）
vector<ArcPoint> bottomLeftArc = {
    ArcPoint(5, 0), ArcPoint(4, 0), ArcPoint(4, 1), ArcPoint(5, 1)
};

// 右下角弧线路径（顺时针）
vector<ArcPoint> bottomRightArc = {
    ArcPoint(5, 5), ArcPoint(4, 5), ArcPoint(4, 4), ArcPoint(5, 4)
};

// 所有弧线的集合
vector<vector<ArcPoint>*> allArcs = {&topLeftArc, &topRightArc, &bottomLeftArc, &bottomRightArc};

// 检查两点是否在同一弧线上
bool isOnSameArc(int startX, int startY, int endX, int endY, const vector<ArcPoint>& arc, int& startIdx, int& endIdx) {
    startIdx = -1;
    endIdx = -1;

    // 查找起点和终点在弧线中的位置
    for (size_t i = 0; i < arc.size(); i++) {
        if (startX == arc[i].x && startY == arc[i].y) {
            startIdx = i;
        }
        if (endX == arc[i].x && endY == arc[i].y) {
            endIdx = i;
        }
    }

    return (startIdx != -1 && endIdx != -1 && startIdx != endIdx);
}

// 检查弧线路径上是否有阻挡
bool isArcPathClear(const vector<ArcPoint>& arc, int startIdx, int endIdx) {
    int arcSize = arc.size();

    // 计算两个方向的路径长度
    int clockwiseSteps = (endIdx - startIdx + arcSize) % arcSize;
    int counterClockwiseSteps = (startIdx - endIdx + arcSize) % arcSize;

    // 选择较短的路径
    bool useClockwise = clockwiseSteps <= counterClockwiseSteps;
    int steps = useClockwise ? clockwiseSteps : counterClockwiseSteps;

    // 检查路径上的每个点（不包括起点和终点）
    for (int i = 1; i < steps; i++) {
        int checkIdx;
        if (useClockwise) {
            checkIdx = (startIdx + i) % arcSize;
        } else {
            checkIdx = (startIdx - i + arcSize) % arcSize;
        }

        int checkX = arc[checkIdx].x;
        int checkY = arc[checkIdx].y;

        // 如果路径上有己方棋子，则路径被阻挡
        if (Board[checkX][checkY] == computerSide) {
            return false;
        }
    }

    return true;
}

// 使用BFS寻找通过弧线的路径
bool findArcPath(int startX, int startY, int endX, int endY) {
    // 如果起点和终点相同，不能吃子
    if (startX == endX && startY == endY) {
        return false;
    }

    // 检查每个弧线
    for (const auto& arc : allArcs) {
        int startIdx, endIdx;
        if (isOnSameArc(startX, startY, endX, endY, *arc, startIdx, endIdx)) {
            // 检查弧线路径是否畅通
            if (isArcPathClear(*arc, startIdx, endIdx)) {
                return true;
            }
        }
    }

    return false;
}


// 检查是否可以通过弧线进行吃子的主函数
bool isValidArcCapture(int startX, int startY, int endX, int endY) {
    // 检查边界
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有己方棋子
    if (Board[startX][startY] != computerSide) {
        return false;
    }

    // 检查目标位置是否有对方棋子
    if (Board[endX][endY] != (computerSide ^ 1)) {
        return false;
    }

    // 使用改进的弧线路径查找
    return findArcPath(startX, startY, endX, endY);
}

// 获取所有可能的弧线吃子移动
vector<pair<ArcPoint, ArcPoint>> getAllArcCaptureMoves(int side) {
    vector<pair<ArcPoint, ArcPoint>> moves;

    // 遍历棋盘上的所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == side) {
                ArcPoint start(i, j);

                // 检查所有可能的目标位置（对方棋子）
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (side ^ 1)) {
                            ArcPoint end(ti, tj);

                            // 临时设置computerSide以便检查
                            int originalSide = computerSide;
                            computerSide = side;

                            if (isValidArcCapture(i, j, ti, tj)) {
                                moves.push_back(make_pair(start, end));
                            }

                            // 恢复原始设置
                            computerSide = originalSide;
                        }
                    }
                }
            }
        }
    }

    return moves;
}

// 调试函数：打印弧线信息
void printArcInfo() {
    cout << "苏拉卡尔塔棋弧线信息:" << endl;
    cout << "左上角弧线: ";
    for (const auto& point : topLeftArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "右上角弧线: ";
    for (const auto& point : topRightArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "左下角弧线: ";
    for (const auto& point : bottomLeftArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;

    cout << "右下角弧线: ";
    for (const auto& point : bottomRightArc) {
        cout << "(" << point.x << "," << point.y << ") ";
    }
    cout << endl;
}