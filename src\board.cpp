#include "../include/board.h"
#include "../include/arc_capture.h"

// 全局变量定义
int Board[BOARD_SIZE][BOARD_SIZE];  // 棋盘结构
int computerSide;                   // 己方执棋颜色
int gameStarted = 0;               // 对局开始标记

// 初始化棋盘
void initializeBoard() {
    int i, j;

    // 初始化棋盘为空
    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }

    // 设置初始棋子位置
    // 黑子在上方两行
    for (i = 0; i < 2; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = BLACK;
        }
    }

    // 白子在下方两行
    for (i = 4; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = WHITE;
        }
    }
}

// 打印棋盘（调试用）
void printBoard() {
    cout << "  A B C D E F" << endl;
    for (int i = 0; i < BOARD_SIZE; i++) {
        cout << (i + 1) << " ";
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == BLACK) {
                cout << "B ";
            } else if (Board[i][j] == WHITE) {
                cout << "W ";
            } else {
                cout << ". ";
            }
        }
        cout << endl;
    }
    cout << endl;
}

// 检查是否为有效的基本移动（8方向，一格）
int isValidMove(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否为空（不能与其他棋子产生冲突）
    if (Board[end.x][end.y] != EMPTY) {
        return 0;
    }

    // 检查是否为8方向移动一格
    int dx = abs(end.x - start.x);
    int dy = abs(end.y - start.y);

    // 8个方向：上下左右和4个对角线方向，每次只能移动一格
    if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
        return 1;
    }

    return 0;
}

// 吃子判断（使用弧线路径检查）
int canCapture(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否有对方棋子
    if (Board[end.x][end.y] != (computerSide ^ 1)) {
        return 0;
    }

    // 简化的弧线吃子实现
    return isSimpleArcCapture(start.x, start.y, end.x, end.y);
}

// 执行移动 - 完全重写，添加严格验证
int makeMove(Point start, Point end) {
    // 第一步：严格的边界检查
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 第二步：检查起始位置必须有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 第三步：检查起始位置和目标位置不能相同
    if (start.x == end.x && start.y == end.y) {
        return 0;
    }

    // 第四步：尝试普通移动
    if (isValidMove(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    }

    // 第五步：尝试吃子移动
    if (canCapture(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    }

    // 如果都不满足，移动失败
    return 0;
}

// 计算指定颜色的棋子数量
int countPieces(int color) {
    int count = 0;

    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == color) {
                count++;
            }
        }
    }

    return count;
}

// 检查是否有可能的移动
int hasValidMoves(int side) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == side) {
                Point start(i, j);

                // 检查8方向的普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;

                        Point end(i + di, j + dj);
                        if (end.x >= 0 && end.x < BOARD_SIZE &&
                            end.y >= 0 && end.y < BOARD_SIZE &&
                            Board[end.x][end.y] == EMPTY) {
                            return 1;
                        }
                    }
                }

                // 检查弧线吃子
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (side ^ 1)) {
                            Point end(ti, tj);
                            if (canCapture(start, end)) {
                                return 1;
                            }
                        }
                    }
                }
            }
        }
    }
    return 0;
}

// 检查游戏是否结束
int isGameOver() {
    int blackCount = countPieces(BLACK);
    int whiteCount = countPieces(WHITE);

    // 如果任一方棋子全部被吃掉
    if (blackCount == 0 || whiteCount == 0) {
        return 1;
    }

    // 检查是否还有可能的移动
    if (!hasValidMoves(BLACK) && !hasValidMoves(WHITE)) {
        return 1;
    }

    return 0;
}

// 复制棋盘
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            dest[i][j] = src[i][j];
        }
    }
}

// 验证移动的完整性（调试用）
bool validateMove(Point start, Point end) {
    // 基本边界检查
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return false;
    }

    // 检查是否是有效的普通移动或吃子移动
    return (isValidMove(start, end) || canCapture(start, end));
}

// 简化的弧线吃子实现
int isSimpleArcCapture(int startX, int startY, int endX, int endY) {
    // 基本验证
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[startX][startY] != computerSide) {
        return 0;
    }

    // 检查目标位置是否有对方棋子
    if (Board[endX][endY] != (computerSide ^ 1)) {
        return 0;
    }

    // 简化的弧线检查：只检查四个角落的弧线
    // 左上角弧线：(0,0) -> (0,1) -> (1,1) -> (1,0)
    if (isOnCornerArc(startX, startY, endX, endY, 0, 0)) return 1;

    // 右上角弧线：(0,5) -> (0,4) -> (1,4) -> (1,5)
    if (isOnCornerArc(startX, startY, endX, endY, 0, 5)) return 1;

    // 左下角弧线：(5,0) -> (4,0) -> (4,1) -> (5,1)
    if (isOnCornerArc(startX, startY, endX, endY, 5, 0)) return 1;

    // 右下角弧线：(5,5) -> (4,5) -> (4,4) -> (5,4)
    if (isOnCornerArc(startX, startY, endX, endY, 5, 5)) return 1;

    return 0;
}

// 检查是否在指定角落的弧线上
int isOnCornerArc(int startX, int startY, int endX, int endY, int cornerX, int cornerY) {
    // 定义每个角落的弧线点
    int arcPoints[4][2];

    if (cornerX == 0 && cornerY == 0) {
        // 左上角
        arcPoints[0][0] = 0; arcPoints[0][1] = 0;
        arcPoints[1][0] = 0; arcPoints[1][1] = 1;
        arcPoints[2][0] = 1; arcPoints[2][1] = 1;
        arcPoints[3][0] = 1; arcPoints[3][1] = 0;
    } else if (cornerX == 0 && cornerY == 5) {
        // 右上角
        arcPoints[0][0] = 0; arcPoints[0][1] = 5;
        arcPoints[1][0] = 0; arcPoints[1][1] = 4;
        arcPoints[2][0] = 1; arcPoints[2][1] = 4;
        arcPoints[3][0] = 1; arcPoints[3][1] = 5;
    } else if (cornerX == 5 && cornerY == 0) {
        // 左下角
        arcPoints[0][0] = 5; arcPoints[0][1] = 0;
        arcPoints[1][0] = 4; arcPoints[1][1] = 0;
        arcPoints[2][0] = 4; arcPoints[2][1] = 1;
        arcPoints[3][0] = 5; arcPoints[3][1] = 1;
    } else if (cornerX == 5 && cornerY == 5) {
        // 右下角
        arcPoints[0][0] = 5; arcPoints[0][1] = 5;
        arcPoints[1][0] = 4; arcPoints[1][1] = 5;
        arcPoints[2][0] = 4; arcPoints[2][1] = 4;
        arcPoints[3][0] = 5; arcPoints[3][1] = 4;
    } else {
        return 0;
    }

    // 查找起点和终点在弧线中的位置
    int startIdx = -1, endIdx = -1;
    for (int i = 0; i < 4; i++) {
        if (startX == arcPoints[i][0] && startY == arcPoints[i][1]) {
            startIdx = i;
        }
        if (endX == arcPoints[i][0] && endY == arcPoints[i][1]) {
            endIdx = i;
        }
    }

    // 如果起点和终点都在弧线上且不相同
    if (startIdx != -1 && endIdx != -1 && startIdx != endIdx) {
        // 简化：不检查路径阻挡，直接允许弧线吃子
        return 1;
    }

    return 0;
}
