#ifndef ARC_CAPTURE_H
#define ARC_CAPTURE_H

#include "common.h"
#include <queue>

// 弧线路径定义
struct ArcPoint {
    int x, y;
    ArcPoint() : x(0), y(0) {}
    ArcPoint(int x, int y) : x(x), y(y) {}

    bool operator==(const ArcPoint& other) const {
        return x == other.x && y == other.y;
    }
};

// 弧线吃子相关函数声明
bool isValidArcCapture(int startX, int startY, int endX, int endY);
bool isValidArcMove(int startX, int startY, int endX, int endY);
bool canReachThroughArc(int startX, int startY, int endX, int endY);
bool findComplexArcPath(int startX, int startY, int endX, int endY);
bool canMoveDirectly(int startX, int startY, int endX, int endY);
bool isOnArc(int x, int y, const vector<ArcPoint>& arc);
int getArcIndex(int x, int y, const vector<ArcPoint>& arc);
bool isArcPathClear(const vector<ArcPoint>& arc, int startIdx, int endIdx);
vector<pair<ArcPoint, ArcPoint>> getAllArcCaptureMoves(int side);
void printArcInfo();

#endif // ARC_CAPTURE_H
