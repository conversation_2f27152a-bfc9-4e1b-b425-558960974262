#ifndef ARC_CAPTURE_H
#define ARC_CAPTURE_H

#include "common.h"
#include <queue>

// 弧线路径定义
struct ArcPoint {
    int x, y;
    ArcPoint() : x(0), y(0) {}
    ArcPoint(int x, int y) : x(x), y(y) {}

    bool operator==(const ArcPoint& other) const {
        return x == other.x && y == other.y;
    }
};

// 弧线吃子相关函数声明
bool isValidArcCapture(int startX, int startY, int endX, int endY);
bool findArcPath(int startX, int startY, int endX, int endY);
vector<pair<ArcPoint, ArcPoint>> getAllArcCaptureMoves(int side);
void printArcInfo();

#endif // ARC_CAPTURE_H
